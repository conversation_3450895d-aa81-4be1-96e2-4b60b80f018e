import 'package:flutter_local_notifications/flutter_local_notifications.dart';
import 'package:flutter_timezone/flutter_timezone.dart';
import 'package:timezone/timezone.dart' as tz;

class NotificationService {
  FlutterLocalNotificationsPlugin flutterLocalNotificationsPlugin =
      FlutterLocalNotificationsPlugin();

  initNotification() async {
    var initializationSettingsAndroid =
        const AndroidInitializationSettings('logo');
    var initializationSettingsIOS = DarwinInitializationSettings(
      requestAlertPermission: false, // Permission requested manually
      requestBadgePermission: false,
      requestSoundPermission: false,
      onDidReceiveLocalNotification:
          (int id, String? title, String? body, String? payload) async {},
    );
    var initializationSettings = InitializationSettings(
      android: initializationSettingsAndroid,
      iOS: initializationSettingsIOS,
    );
    await flutterLocalNotificationsPlugin.initialize(initializationSettings);
  }

  cancelNotification() async {
    await flutterLocalNotificationsPlugin.cancelAll();
  }

  notificationDetails() {
    return const NotificationDetails(
        android: AndroidNotificationDetails("channelId", " channelName",
            styleInformation: BigTextStyleInformation(''),
            priority: Priority.high,
            importance: Importance.max),
        iOS: DarwinNotificationDetails());
  }

  showNotification({required String title, required String body}) async {
    await flutterLocalNotificationsPlugin.show(
      1, // Unique ID for the notification
      title,
      body,
      notificationDetails(),
    );
  }

  Future<void> scheduleNotification() async {
    final String currentTimeZone = await FlutterTimezone.getLocalTimezone();

    tz.setLocalLocation(tz.getLocation(currentTimeZone));

    final tz.TZDateTime scheduledNotificationDateTime = tz.TZDateTime.from(
        DateTime.now().add(const Duration(days: 6)), tz.local);
    // DateTime.now().add(const Duration(seconds: 30)),
    // tz.local);

    await flutterLocalNotificationsPlugin.zonedSchedule(
      1, // Unique ID for the notification
      'Vaša probna verzija ističe za 24 sata.',
      null,
      scheduledNotificationDateTime,
      notificationDetails(),
      androidScheduleMode: AndroidScheduleMode.exactAllowWhileIdle,
      uiLocalNotificationDateInterpretation:
          UILocalNotificationDateInterpretation.absoluteTime,
    );
  }
}
