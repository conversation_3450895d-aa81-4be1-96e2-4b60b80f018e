import 'package:bibl/res/style.dart';
import 'package:flutter/material.dart';
import 'package:flutter_svg/flutter_svg.dart';
import 'package:get/get.dart';
import '../controllers/profile_controller.dart';

class ProfilePhotoWidget extends StatelessWidget {
  final bool isOnSettings;
  const ProfilePhotoWidget({
    Key? key,
    required this.isOnSettings,
  }) : super(key: key);

  // List of available profile images
  static const List<String> profileImages = [
    'assets/images/img1.png',
    'assets/images/img2.png',
    'assets/images/img3.png',
    'assets/images/img4.png',
  ];

  // Show bottom sheet with image selection
  void _showImagePicker(BuildContext context) {
    final ProfileController profileController = Get.find();

    showModalBottomSheet(
      context: context,
      shape: const RoundedRectangleBorder(
        borderRadius: BorderRadius.vertical(top: Radius.circular(20)),
      ),
      builder: (context) {
        return Padding(
          padding: const EdgeInsets.all(16.0),
          child: Column(
            mainAxisSize: MainAxisSize.min,
            children: [
              const Text(
                'Choose Avatar',
                style: TextStyle(fontSize: 18, fontWeight: FontWeight.bold),
              ),
              const SizedBox(height: 16),
              GridView.builder(
                shrinkWrap: true,
                gridDelegate: const SliverGridDelegateWithFixedCrossAxisCount(
                  crossAxisCount: 2,
                  crossAxisSpacing: 10,
                  mainAxisSpacing: 10,
                  childAspectRatio: 1,
                ),
                itemCount: profileImages.length,
                itemBuilder: (context, index) {
                  final imagePath = profileImages[index];
                  return GestureDetector(
                    onTap: () async {
                      Get.back();
                      // Update profile photo
                      await profileController.updateProfilePhoto(imagePath);
                    },
                    child: ClipRRect(
                      borderRadius: BorderRadius.circular(10),
                      child: Image.asset(
                        imagePath,
                        fit: BoxFit.cover,
                      ),
                    ),
                  );
                },
              ),
            ],
          ),
        );
      },
    );
  }

  @override
  Widget build(BuildContext context) {
    final ProfileController profileController = Get.find();

    return Row(
      mainAxisAlignment: MainAxisAlignment.center,
      children: [
        Obx(() {
          final profilePhoto = profileController.userr.value.profilePhoto ?? '';
          final hasPhoto = profilePhoto.isNotEmpty;
          double radius = isOnSettings ? 25 : 50;
          Widget avatar = hasPhoto
              ? Container(
                  width: radius * 2,
                  height: radius * 2,
                  decoration: const BoxDecoration(
                    shape: BoxShape.circle,
                  ),
                  child: ClipOval(
                    child: Image.asset(
                      profilePhoto,
                      fit: BoxFit.cover,
                      // alignment: Alignment.topCenter, // 👈 head upar rakhta hai
                    ),
                  ))
              : SvgPicture.asset(
                  'assets/svgs/camera_icon.svg',
                  height: isOnSettings ? 16 : 30,
                );

          if (isOnSettings) {
            return avatar;
          } else {
            return SizedBox(
              height: 100,
              width: 110,
              child: Stack(
                children: [
                  avatar,
                  Align(
                    alignment: Alignment.bottomRight,
                    child: GestureDetector(
                      onTap: () => _showImagePicker(context),
                      child: CircleAvatar(
                        radius: 20,
                        backgroundColor: grey2Color,
                        child: CircleAvatar(
                          radius: 18,
                          backgroundColor: Colors.white,
                          child:
                              SvgPicture.asset('assets/svgs/camera_icon.svg'),
                        ),
                      ),
                    ),
                  ),
                ],
              ),
            );
          }
        }),
      ],
    );
  }
}
